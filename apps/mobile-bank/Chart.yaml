apiVersion: v2
name: mobile-bank
version: 0.1.0
description: Parent chart for mobile-bank microservices
type: application
dependencies:
  - name: fpaas-apigw-http
    version: 0.1.0
    repository: "file://charts/fpaas-apigw-http"
    condition: fpaas-apigw-http.enabled
  - name: fpaas-deploy-service
    version: 0.1.0
    repository: "file://charts/fpaas-deploy-service"
    condition: fpaas-deploy-service.enabled
  - name: fpaas-gateway-service
    version: 0.1.0
    repository: "file://charts/fpaas-gateway-service"
    condition: fpaas-gateway-service.enabled
  - name: fpaas-product-online
    version: 0.1.0
    repository: "file://charts/fpaas-product-online"
    condition: fpaas-product-online.enabled
  - name: fpaas-portal
    version: 0.1.0
    repository: "file://charts/fpaas-portal"
    condition: fpaas-portal.enabled
  - name: fpaas-strategy-online
    version: 0.1.0
    repository: "file://charts/fpaas-strategy-online"
    condition: fpaas-strategy-online.enabled
  - name: rbmp-usercenter
    version: 0.1.0
    repository: "file://charts/rbmp-usercenter"
    condition: rbmp-usercenter.enabled
  - name: rbmp-transactioncenter
    version: 0.1.0
    repository: "file://charts/rbmp-transactioncenter"
    condition: rbmp-transactioncenter.enabled
  - name: rbmp-route
    version: 0.1.0
    repository: "file://charts/rbmp-route"
    condition: rbmp-route.enabled
  - name: rbmp-bm-online
    version: 0.1.0
    repository: "file://charts/rbmp-bm-online"
    condition: rbmp-bm-online.enabled
  - name: rbmp-processcenter
    version: 0.1.0
    repository: "file://charts/rbmp-processcenter"
    condition: rbmp-processcenter.enabled
  - name: rbmp-paramcenter
    version: 0.1.0
    repository: "file://charts/rbmp-paramcenter"
    condition: rbmp-paramcenter.enabled
  - name: rbmp-limitcenter
    version: 0.1.0
    repository: "file://charts/rbmp-limitcenter"
    condition: rbmp-limitcenter.enabled
  - name: rbmp-authoritycenter
    version: 0.1.0
    repository: "file://charts/rbmp-authoritycenter"
    condition: rbmp-authoritycenter.enabled
  - name: rbmp-authencenter
    version: 0.1.0
    repository: "file://charts/rbmp-authencenter"
    condition: rbmp-authencenter.enabled
  - name: mobile-bank-dist
    version: 0.1.0
    repository: "file://charts/mobile-bank-dist"
    condition: mobile-bank-dist.enabled