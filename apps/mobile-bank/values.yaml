global:
  # 全局命名空间配置
  namespace: "onebank"

  # Default image host
  imageHost: "************.dkr.ecr.ap-southeast-1.amazonaws.com"

   # Default image pull policy
  imagePullPolicy: "IfNotPresent"


  # Filebeat 配置
  filebeat:
    image:
      repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/galaxy/filebeat
      tag: ********
      pullPolicy: Always
    resources:
      limits:
        cpu: 500m
        memory: 500Mi
      requests:
        cpu: 30m
        memory: 500Mi
  
  defaultGalaxy:
    tenantId: fcsjlwj1gk9s
    profile: y6vklwj509pg
    dataCenter: dc01

  middleware:
    # Default database configuration
    defaultDatabase:
      driver: "com.mysql.cj.jdbc.Driver"
      host: "************************************************************************************************"
      hostname: "k8s-paas-mysqlser-d42e0f7fc9-41a1e44b924c815b.elb.ap-southeast-1.amazonaws.com"
      username: "root"
      password: "rootpasswd#45r"

    # Default platform configuration
    defaultPlatform:
      #废弃
      eureka: dcits.cbs.eureka.fat1:9527
      nacos: 
        address: nacosx-headless.paas:8848
        namespace: "public"
      orbit:
        enable: true
      mars: mars.galaxy:9994
      gateway: dcits.cbs.gateway.fat1:8081
      sonic: 
        schedulerAddresses: sonic.galaxy:9999
        serviceAddresses: sonic.galaxy:9901
        lock:
          dbType: mysql
      mq: 
        producer: rocketmq-nameserver.onebank:9876
        consumer: rocketmq-nameserver.onebank:9876
      redis:
        database: 0
        host: redis-standalone-redis.paas
        port: 6379
        password: ""
        ssl: false
        timeout: 1200
      file:
        passwd: cbs001
        serverIp: ***************
        port: 5001
      apollo: 
        enable: false
        cluster: fat
        url: http://dcits.cbs.apoconf.fat1:8080
      skywalking:
        path: /app/galaxy/skywalking-agent/agent
        url: skywalking-oap.galaxy:11800
      logstash:
        service: logstash-service.galaxy:9600
      kafka:
        service: kafka.galaxy:9092
      sso:
        url: http://**************:8085
      oauth2:
        userInfoUri: "http://**********:18187/oms/uaa/user/auth"
      minio:
        endpoint: minio.minio-dev:9000
        accessKey: minioadmin
        secretKey: minioadmin
        buckets: fpaas-bucket
        view: http://minio.minio-dev:9000


  # 全局数据库配置
  # mysql:
  #   host: k8s-paas-mysqlser-d42e0f7fc9-41a1e44b924c815b.elb.ap-southeast-1.amazonaws.com
  #   port: 3306
  #   database: rbmp_usercenter
  #   username: root
  #   password: rootpasswd#45r

  # 全局Redis配置
  # redis:
  #   database: 0
  #   host: redis-standalone-redis.paas
  #   port: 6379
  #   password: ""
  #   ssl: false
  #   timeout: 1200

  # 全局Nacos配置
  # nacos:
  #   host: "tellerx-nacos-server.paas"
  #   port: 8848
  #   namespace: "public"

  # 全局Kafka配置
  kafka:
    service: "kafka.galaxy:9092"
    host: "kafka.galaxy"
    port: 9092

  # logstash:
  #   service: logstash-service.galaxy:9600

  # skywalking:
  #   path: /app/galaxy/skywalking-agent/agent
  #   url: skywalking-oap.galaxy:11800

  # defaultGalaxy:
  #   tenantId: fcsjlwj1gk9s
  #   profile: y6vklwj509pg
  #   dataCenter: dc01

  # 全局Mars配置
  # mars:
  #   host: "mars.galaxy"
  #   port: 9994

  # 全局SSO配置
  # sso:
  #   url: "http://**************:8085"

  # # 全局OAuth2配置
  # oauth2:
  #   userInfoUri: "http://**********:18187/oms/uaa/user/auth"

  # 全局持久化存储配置
  persistence:
    enabled: false  # 暂时禁用持久化存储
    storageClass: "gp2"  # AWS EKS默认存储类
    accessMode: ReadWriteOnce
    size: 10Gi
    mountPath: /app/logs

  # 全局应用配置
  config:
    tokenRefreshTime: 30000
    tokenTimeout: 300000000
    httpMaxSize: "10485760"
    profile: "fat"  # 默认环境配置

  # 全局资源配置
  resources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "128Mi"

  # 全局JVM配置
  jvm:
    xms: "1024m"
    xmx: "1024m"
    xmn: "256m"
    metaspaceSize: "512m"
    maxMetaspaceSize: "1024m"

  # 全局安全上下文
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000

  # 全局Pod安全上下文
  podSecurityContext:
    fsGroup: 1000

  # 全局MinIO配置
  # minio:
  #   endpoint: minio.minio-dev:9000
  #   accessKey: minioadmin
  #   secretKey: minioadmin
  #   buckets: fpaas-bucket
  #   view: http://minio.minio-dev:9000

  # 服务配置
  service:
    type: LoadBalancer
    port: 80
    targetPort: 19020
    annotations:
      service.beta.kubernetes.io/aws-load-balancer-type: "nlb"

  # 监控配置
  monitoring:
    enabled: true
    serviceMonitor:
      enabled: false
      namespace: monitoring
      interval: 30s
      path: /actuator/prometheus

# 服务特定配置
fpaas-apigw-http:
  enabled: true
fpaas-deploy-service:
  enabled: true
fpaas-gateway-service:
  enabled: true
fpaas-product-online:
  enabled: true
fpaas-portal:
  enabled: true
fpaas-strategy-online:
  enabled: true
rbmp-usercenter:
  enabled: true
rbmp-transactioncenter:
  enabled: true
rbmp-route:
  enabled: true
rbmp-bm-online:
  enabled: true
rbmp-processcenter:
  enabled: true
rbmp-paramcenter:
  enabled: false
rbmp-limitcenter:
  enabled: true
rbmp-authoritycenter:
  enabled: true
rbmp-authencenter:
  enabled: true
mobile-bank-dist:
  enabled: true