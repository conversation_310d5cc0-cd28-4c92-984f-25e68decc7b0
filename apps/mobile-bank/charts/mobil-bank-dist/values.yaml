# Override the full name of the chart
fullnameOverride: "mobile-bank-dist"

# Ingress configuration
ingress:
  enabled: true
  className: "alb"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
  hosts:
    - host: ""  # 替换为您的实际域名
      paths:
        - path: /rbmp
          pathType: Prefix
          serviceName: rbmp
          servicePort: 6084
        - path: /rbmpweb
          pathType: Prefix
          serviceName: rbmpweb
          servicePort: 9177
        - path: /rbmppassec
          pathType: Prefix
          serviceName: rbmppassec
          servicePort: 9173
        - path: /rbmppass
          pathType: Prefix
          serviceName: rbmppass
          servicePort: 9174
        - path: /
          pathType: Prefix
          serviceName: rbmp
          servicePort: 6084
  tls: []
# Service configuration
service:
  type: ClusterIP
  rbmp:
    port: 6084
    targetPort: 6084
  rbmpweb:
    port: 9177
    targetPort: 9177
  rbmppassec:
    port: 9173
    targetPort: 9173
  rbmppass:
    port: 9174
    targetPort: 9174

# Replica count
replicas: 1

# Image configuration
image:
  repository: "{{ .Values.global.imageHost }}/onebank/mobile-bank-dist"
  tag: "1.1.0-alpha"
  pullPolicy: "IfNotPresent"

# Resource limits and requests
resources:
  limits:
    cpu: "200m"
    memory: "256Mi"
  requests:
    cpu: "100m"
    memory: "128Mi"

# ConfigMap for nginx configuration
nginxConfig:
  enabled: true
  configPath: "/etc/nginx/conf/vhost"
  # Main nginx configuration content will be mounted from configmap

# Liveness and readiness probes
livenessProbe:
  httpGet:
    path: "/"
    port: 9900
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: "/"
    port: 9900
  initialDelaySeconds: 10
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3

# Pod security context is not needed, use default user