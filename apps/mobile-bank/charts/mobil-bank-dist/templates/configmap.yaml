apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "mobile-bank-dist.fullname" . }}-nginx-config
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    {{- include "mobile-bank-dist.labels" . | nindent 4 }}
data:
  nginx.conf: |-
    {{- tpl (.Files.Get "files/nginx.conf") . | nindent 4 }}
  dmz.conf: |-
    {{- tpl (.Files.Get "files/vhost/dmz.conf") . | nindent 4 }}
  fcp-ec.conf: |-
    {{- tpl (.Files.Get "files/vhost/fcp-ec.conf") . | nindent 4 }}
  fpaas-ec.conf: |-
    {{- tpl (.Files.Get "files/vhost/fpaas-ec.conf") . | nindent 4 }}
  fpaas.conf: |-
    {{- tpl (.Files.Get "files/vhost/fpaas.conf") . | nindent 4 }}