apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "mobile-bank-dist.fullname" . }}
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    {{- include "mobile-bank-dist.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicas | default 1 }}
  selector:
    matchLabels:
      {{- include "mobile-bank-dist.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "mobile-bank-dist.selectorLabels" . | nindent 8 }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ tpl .Values.image.repository $ }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
          - name: nginx-config
            subPath: nginx.conf
            mountPath: {{ .Values.nginxConfig.configPath }}/conf/nginx.conf
          - name: nginx-config
            subPath: dmz.conf
            mountPath: {{ .Values.nginxConfig.configPath }}/conf/vhost/dmz.conf
          - name: nginx-config
            subPath: fcp-ec.conf
            mountPath: {{ .Values.nginxConfig.configPath }}/conf/vhost/fcp-ec.conf
          - name: nginx-config
            subPath: fpaas-ec.conf
            mountPath: {{ .Values.nginxConfig.configPath }}/conf/vhost/fpaas-ec.conf
          - name: nginx-config
            subPath: fpaas.conf
            mountPath: {{ .Values.nginxConfig.configPath }}/conf/vhost/fpaas.conf
      volumes:
        - name: nginx-config
          configMap:
            name: {{ include "mobile-bank-dist.fullname" . }}-nginx-config
            items:
            - key: nginx.conf   # ConfigMap中的key
              path: nginx.conf
            - key: dmz.conf   # ConfigMap中的key
              path: dmz.conf
            - key: fcp-ec.conf   # ConfigMap中的key
              path: fcp-ec.conf
            - key: fpaas-ec.conf   # ConfigMap中的key
              path: fpaas-ec.conf
            - key: fpaas.conf   # ConfigMap中的key
              path: fpaas.conf