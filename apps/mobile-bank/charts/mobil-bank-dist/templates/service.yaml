apiVersion: v1
kind: Service
metadata:
  name: {{ include "mobile-bank-dist.fullname" . }}
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    {{- include "mobile-bank-dist.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.rbmp.port }}
      targetPort: {{ .Values.service.rbmp.targetPort }}
      protocol: TCP
      name: rbmp
    - port: {{ .Values.service.rbmpweb.port }}
      targetPort: {{ .Values.service.rbmpweb.targetPort }}
      protocol: TCP
      name: rbmpweb
    - port: {{ .Values.service.rbmppassec.port }}
      targetPort: {{ .Values.service.rbmppassec.targetPort }}
      protocol: TCP
      name: rbmppassec
    - port: {{ .Values.service.rbmppass.port }}
      targetPort: {{ .Values.service.rbmppass.targetPort }}
      protocol: TCP
      name: rbmppass
  selector:
    {{- include "mobile-bank-dist.selector<PERSON>abels" . | nindent 4 }}