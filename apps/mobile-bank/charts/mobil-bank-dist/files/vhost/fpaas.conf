upstream dynamic_objs {
  least_conn;    
  server minio-host:9000;
}

upstream upstream_apigw {
  #ip_hash;
  server fpaas-webapi:8084;
  #server fpaas-webapi:8085;
}
upstream ups-file-upload {
  # 根据ip固定hash到同一节点，避免文件的分片落在不同节点无法合并
  ip_hash;
  server ***************:19020;
  #server ip2:19020;
}

server {
  listen       8998;

  # fpaas文件上传，注意要放在/webapi/这个location前面
  location /webapi/FPAAS-FILE-UPLOAD/ {
      # 报文大小限制
      client_max_body_size 100m;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://ups-file-upload/;
  }
  
  location /webapi/ {
    # 这里配置upstream会导致400，要加上 proxy_set_header Host $http_host;
    proxy_pass http://upstream_apigw/inner/;
    proxy_set_header 'app-id' '88888888';
    proxy_set_header 'workspace-id' 'default';
    proxy_set_header Host $http_host;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
  }
  location /atws/ {
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;
      proxy_pass http://fpaas-websocket:8086/innerws/;
  }
  location /fpaas_bucket/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket/;
  }
  location /fpaas-bucket/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket/;
  }
  location /scrm-bucket/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/scrm-bucket/;
  }

}
server {
  listen       8999;
  charset utf-8;

  location /WW_verify_eqf0lSfiVeFI2GcW.txt {
    charset utf-8;
    root html;
  }

  # fpaas文件上传，注意要放在/webapi/这个location前面
  location /webapi/FPAAS-FILE-UPLOAD/ {
      # 报文大小限制
      client_max_body_size 100m;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://ups-file-upload/;
  }

  #scrm前端加载企微图片
  location /webapi/rbmp-scrm-media/media {
      proxy_set_header 'app-id' '75754082';
      proxy_set_header 'workspace-id' 'default';
      proxy_pass http://fpaas-webapi:8084/inner/rbmp-scrm-media/media;
  }
  
  location /webapi/ {
    proxy_pass http://upstream_apigw/at/;
    proxy_set_header Host $http_host;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
  }
location /at/webapi/ {
    proxy_pass http://upstream_apigw/at/;
    proxy_set_header Host $http_host;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
  }
  location /webapi2/ {
    proxy_pass http://upstream_apigw/at/;
    proxy_set_header Host $http_host;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
  }
  location /atws/ {
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;
      proxy_pass http://fpaas-websocket:8086/atws/;
  }
  location /fpaas_bucket/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket/;
  }

  location /fpaas-bucket-poc/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket-poc/;
  }  

  location /fpaas-bucket/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket/;
  }
  location /scrm-bucket/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/scrm-bucket/;
  }

 location /ccsdk {
      proxy_pass http://192.168.164.213:8081;
  }

  #数据分析日志网关
  location ^~/api/v1/ {
        proxy_pass http://10.0.24.142:8443;
  }
  location /dev/ {
     charset utf-8;
        root html;
  }
  location = /apple-app-site-association {
    root html;
  }
  #权益商城
  location ^~ /smartmall-view {
    root html;
    index  index.html;
  }
  location ^~/SmartMall {
    proxy_pass http://**************:8881;
  }
  location ^~/coupon {
    proxy_pass http://**************:8881;
  }
  location ^~/images {
    proxy_pass http://**************:8881;
  }
  location ^~/static/img {
    proxy_pass http://**************:8881;
  }
  
  location /index.html {
    return 404;
  }
}
server {
  listen       8900;
  location / {
      root   /usr/share/nginx/html;
      index  index.html index.htm;
  }
  location /at/ {
      proxy_pass http://fpaas-gw:8082/at/;
  }
  location /minio/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/minio/;
  }
  location /zipkin/ {
      proxy_pass http://fpaas-zipkin:9411/zipkin/;
  }
}

server {
   listen       8080;
   root html/fpaas-view/21000000;
   client_max_body_size 500m;
   add_header Cache-Control no-store;
   #charset koi8-r;

   #access_log  logs/host.access.log  main;

  # 测试用
  location /test/user/info/query {
    proxy_pass http://***************:9811/user/info/query/;
  }

  location /at/fpaas-fdqp-analysis-online/ {
    proxy_pass http://localhost:8998/webapi/fpaas-fdqp-analysis-online/;
  }

  location /at {
       proxy_pass http://fpaas-gw:8082;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
       proxy_set_header X-Forwarded-Port $server_port;
       proxy_connect_timeout 300s;
       proxy_send_timeout 300s;
       proxy_read_timeout 300s;
  }

location /webapi {
       proxy_pass http://localhost:8998;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
       proxy_set_header X-Forwarded-Port $server_port;
       proxy_connect_timeout 300s;
       proxy_send_timeout 300s;
       proxy_read_timeout 300s;
  }


}