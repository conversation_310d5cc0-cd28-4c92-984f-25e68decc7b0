server {
        listen       6084;
        server_name  localhost;
        root /home/<USER>/nginx/html/fcp-view/vela-main;

        #前端服务
        location / {
               root /home/<USER>/nginx/html/fcp-view/vela-main;
               index index.html index.html;
               try_files $uri $uri/ /index.html;
        }
        #SSO
        #location /sso {
        #       try_files $uri $uri/ /index.html;
        #}

        location /fpaas-bucket/ {
          proxy_http_version 1.1;
          proxy_set_header Connection "";
          chunked_transfer_encoding off;
          proxy_pass http://dynamic_objs/fpaas-bucket/;
        }
        location /scrm-bucket/ { 
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            proxy_pass http://dynamic_objs/scrm-bucket/;
        }
        location /fpaas-bucket-poc/ { 
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            proxy_pass http://dynamic_objs/fpaas-bucket-poc/;
        }

        location /fpaas-bucket-test/ {
          proxy_http_version 1.1;
          proxy_set_header Connection "";
          chunked_transfer_encoding off;
          proxy_pass http://dynamic_objs/fpaas-bucket-test/;
        }

        location /oms/uaa {
                # proxy_pass http://**********:9103;
                proxy_pass http://fat-bm-host:9103;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

  location /scrm-bucket-dev/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://**********:9000/scrm-bucket-dev/;
  }
  location /scrm-bucket-test/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://**********:9000/scrm-bucket-test/;
  }
        location /webapi {
             proxy_pass http://localhost:8882;
        }

        
    location /fpaas-bm-online {
       proxy_pass http://localhost:8882/webapi/fpaas-bm-online;
    }


        location /sso/ {
            # proxy_pass http://analysis-host:9802/;
        }


        location /saml {
           #  proxy_pass http://analysis-host:9802;
        }
              
        location /oss {
                proxy_pass http://localhost:9081;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
                proxy_connect_timeout 300s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;
        }
     location ~* /(rbmp|fpaas|rcop)-[a-zA-Z0-9]+-dist {
                root /home/<USER>/nginx/html/fcp-view/;
          index index.html index.htm;
          try_files $uri $uri/ /index.html;
        }

       location /at {
                proxy_pass http://fpaas-gw:8082;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
                proxy_connect_timeout 300s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;
       }

}


# apigw
server {
  listen       7999;
  
  #scrm前端加载企微图片
  location /webapi/rbmp-scrm-media/media {
      proxy_set_header 'app-id' '75754082';
      proxy_set_header 'workspace-id' 'default';
      proxy_pass http://upstream_apigw/inner/rbmp-scrm-media/media;
      proxy_set_header Host $http_host;
      proxy_connect_timeout 300s;
      proxy_send_timeout 300s;
      proxy_read_timeout 300s;
  }
  
  # fpaas文件上传，注意要放在/webapi/这个location前面
  location ~* /webapi/FPAAS-FILE-UPLOAD/(upload|oss)/(.+) {
      # 报文大小限制
      client_max_body_size 100m;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_set_header Host $http_host;
      chunked_transfer_encoding off;
      proxy_pass http://fpaas-webapi:19020/$1/$2?$args;
  }
 
  location /webapi/ {
      proxy_pass http://fpaas-webapi:8084/at/;
  }
  #数据分析日志网关
  location ^~/api/v1/ {
    proxy_pass http://fpaas-webapi:8443;
  }

  location /atws/ {
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;
      proxy_pass http://fpaas-websocket:8086/atws/;
  }
  location /fpaas_bucket/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket-test/;
  }
  
  location /fpaas-bucket-dev/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket-dev/;
  }

  underscores_in_headers on;
  
  location /fpaas-bucket-test/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      # 适配Onebox静态资源访问
      #if ($http_app_id = "51315620") {
      #   proxy_pass http://localhost:9900;
      #   break;
      #}
      proxy_pass http://dynamic_objs/fpaas-bucket-test/;
  }

  location /fpaas-bucket/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket/;
  }

  
    #scrm
  location /scrm-bucket-test/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/scrm-bucket-test/;
  }
  

  
  location /index.html {
    return 404;
  }
}

# fpaas平台2.6.0(930) bm鉴权
server {
   listen       8882;
   root /app/dcits/apps/web/fpaas-view-2.6.0/21000000;
   client_max_body_size 500m;
   add_header Cache-Control no-store;
   #charset koi8-r;

   #access_log  logs/host.access.log  main;

  # fpaas文件上传，注意要放在/webapi/这个location前面
  location ~* /webapi/FPAAS-FILE-UPLOAD/(upload|oss)/(.+) {
      # 报文大小限制
      client_max_body_size 100m;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_set_header Host $http_host;
      chunked_transfer_encoding off;
      proxy_pass http://fpaas-webapi:19020/$1/$2?$args;
  }
  
  location /webapi/ {
     proxy_pass http://fpaas-webapi:8084/inner/;
     proxy_set_header 'app-id' '88888888';
     #proxy_set_header 'workspace-id' 'bm';
     proxy_set_header 'workspace-id' 'default';
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
  }

#  location /at/ {
#     proxy_pass http://fpaas-webapi:8084/inner/;
#     proxy_set_header 'app-id' '88888888';
#     proxy_set_header 'workspace-id' 'bm';
#       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#       proxy_set_header X-Forwarded-Proto $scheme;
#       proxy_set_header X-Forwarded-Port $server_port;
#       proxy_connect_timeout 300s;
#       proxy_send_timeout 300s;
#       proxy_read_timeout 300s;
#  }

  location /fpaas-bucket-dev/ {
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://dynamic_objs/fpaas-bucket-dev/;
  }
  
  location /fcd/ {
     proxy_pass http://fpaas-webapi:8084/inner/;
     proxy_set_header 'app-id' '88888888';
     proxy_set_header 'workspace-id' 'bm';
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
  }

   
}

server {
           listen       9072;
           server_name  localhost;
           location /fpaas-bucket-test/ {
                          proxy_http_version 1.1;
                          proxy_set_header Connection "";
                          chunked_transfer_encoding off;
                          proxy_pass http://dynamic_objs/fpaas-bucket-test/;
                        }
           location /fpaas-bucket/ {
                          proxy_http_version 1.1;
                          proxy_set_header Connection "";
                          chunked_transfer_encoding off;
                          proxy_pass http://dynamic_objs/fpaas-bucket/;
                        }
}