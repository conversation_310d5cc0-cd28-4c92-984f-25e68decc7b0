server {
        listen       9084;
        server_name  localhost;
        root /home/<USER>/nginx/html/rbmp-view/vela-main;

        #前端服务
        location / {
               root /home/<USER>/nginx/html/rbmp-view/vela-main;
               index index.html index.html;
               try_files $uri $uri/ /index.html;
        }
        #SSO
        location /sso {
                try_files $uri $uri/ /index.html;
        }
        location /fpaas-bucket/ {
             proxy_http_version 1.1;
             proxy_set_header Connection "";
             chunked_transfer_encoding off;
             proxy_pass http://dynamic_objs/fpaas-bucket/;
       }
        location /scrm-bucket/ {
             proxy_http_version 1.1;
             proxy_set_header Connection "";
             chunked_transfer_encoding off;
             proxy_pass http://dynamic_objs/scrm-bucket/;
       }
        location /rbmp-portal-dist/ {
                proxy_pass http://localhost:9085/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }  

   

        location /rbmp-product-dist/ {
                proxy_pass http://localhost:9088/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-membership-dist/ {
                proxy_pass http://localhost:9089/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-markingact-dist/ {
                proxy_pass http://localhost:9090/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-cim-dis/ {
                proxy_pass http://localhost:9091/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-equity-dist/ {
                proxy_pass http://localhost:9192/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-usercenter-dist/ {
                proxy_pass http://localhost:9093/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-trancenter-dist/ {
                proxy_pass http://localhost:9094/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-authcenter-dist/ {
                proxy_pass http://localhost:9095/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-strategy-dist/ {
                proxy_pass http://localhost:9096/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-limitcenter-dist/ {
                proxy_pass http://localhost:9098/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-permissions-dist/ {
                proxy_pass http://localhost:9900/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-dataanalysis-dist/ {
                proxy_pass http://localhost:9097/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }


        location /fpaas-analysis-dist/ {
                proxy_pass http://localhost:9099/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /rbmp-scrm-dist/ {
                proxy_pass http://localhost:9101/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /fpaas-workflow-dist/ {
                proxy_pass http://localhost:9102/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }
        location /rbmp-saga-dist/ {
                proxy_pass http://localhost:9104/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }
    location /rbmp-abtest-dist/ {
                proxy_pass http://localhost:9105/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }
        location /rbmp-miniapp-dist/ {
                proxy_pass http://localhost:9106/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

    location /rbmp-tencent-cloud-dist/ {
                proxy_pass http://localhost:9107/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

    location /rbmp-message-dist/ {
                proxy_pass http://localhost:9079/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

    location /fpaas-wechatgw-dist/ {
                proxy_pass http://localhost:9082/;
                proxy_set_header Host $host:$server_port;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /oms/uaa {
                proxy_pass http://fat-bm-host:9103;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
        }

        location /at {
                proxy_pass http://fpaas-gw:8082;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
                proxy_connect_timeout 300s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;
        }

        location /webapi {
             proxy_pass http://localhost:8998;
        }
        location /oss {
                proxy_pass http://localhost:9081;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
                proxy_connect_timeout 300s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;
        }

        location /fpaas-bm-online/ {
                proxy_pass http://fat-bm-host:9103/;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Port $server_port;
                proxy_connect_timeout 300s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;
        }
 
}
# 前端微服务监听
server {
           listen       9085;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-portal-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9088;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-product-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9089;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-membership-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9090;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-markingact-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9091;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-cim-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9192;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-equity-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9093;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-usercenter-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9094;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-trancenter-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9095;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-authcenter-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9096;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-strategy-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9097;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-dataanalysis-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9098;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-limitcenter-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9099;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/fpaas-analysis-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9900;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-permissions-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9101;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-scrm-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9102;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/fpaas-workflow-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9104;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-saga-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}
server {
           listen       9105;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-abtest-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}

server {
           listen       9106;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-miniapp-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}

server {
           listen       9107;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-tencent-cloud-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}

server {
           listen       9079;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/rbmp-message-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}

server {
           listen       9082;
           server_name  localhost;
           add_header Access-Control-Allow-Origin *;
           #underscores_in_headers on;
           location / {
               alias /home/<USER>/nginx/html/rbmp-view/fpaas-wechatgw-dist/;
               index index.html index.htm;
               try_files $uri $uri/ /index.html;
           }
}