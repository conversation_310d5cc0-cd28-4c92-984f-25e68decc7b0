#user  nobody;
worker_processes  1;

error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    client_max_body_size 500m;
    proxy_headers_hash_max_size 512;
    proxy_headers_hash_bucket_size 64;
    sendfile        on;
    gzip on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_http_version 1.0;
    gzip_comp_level 8;
    gzip_types text/plain text/css application/xml application/javascript application/x-font-ttf application/x-font-woff image/jpeg image/gif image/png;
    gzip_vary off;
    gzip_disable "MSIE [1-6]\.";
    keepalive_timeout  80;
    ignore_invalid_headers off;
    underscores_in_headers on;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    map $http_upgrade $connection_upgrade {
      default upgrade;
      '' close;
    }

    include vhost/*.conf;

    server {
        listen       8000;
        server_name  localhost;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location / {
            root   html;
            index  index.html index.htm;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        location ^~ /download {
            alias /app/dcits/apps/web/software;
            if ($request_filename ~* ^.*?\.(html|doc|pdf|zip|tar.gz|gz|docx|txt|apk|jpg|png)$) {
                add_header Content-Disposition attachment;
                add_header Content-Type application/octet-stream;
            }
            autoindex on;
            autoindex_exact_size on;
            autoindex_localtime on;
          charset utf-8,gbk;
        }



    }
}