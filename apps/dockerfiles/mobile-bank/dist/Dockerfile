# 使用官方nginx镜像作为基础镜像，指定平台为linux/amd64
FROM --platform=linux/amd64 nginx:alpine

# 复制nginx配置文件
COPY conf/ /etc/nginx/conf/

# 创建nginx配置中指定的目录结构
RUN mkdir -p /apps/html/fcp-view

# 直接复制前端静态文件到nginx配置中指定的路径
COPY html/ /apps/html/fcp-view/

# 设置文件权限
RUN chown -R nginx:nginx /apps/html/fcp-view && \
    chmod -R 755 /apps/html/fcp-view

# 暴露端口（根据nginx配置中的端口）
EXPOSE 80 6084 7999 8000 8882 8900 8998 8999 9072 9079 9082 9084 9085 9086 9087 9088 9089 9090 9091 9092 9093 9094 9095 9096 9097 9098 9099 9100 9101 9102 9103 9104 9105 9106 9107 9108 9173 9174 9177 9531 9900

# 启动nginx并显式指定配置文件
CMD ["nginx", "-g", "daemon off;", "-c", "/etc/nginx/conf/nginx.conf"]